// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/netceptor/conn.go
//
// Generated by this command:
//
//	mockgen -source=pkg/netceptor/conn.go -destination=pkg/netceptor/mock_netceptor/conn.go
//

// Package mock_netceptor is a generated GoMock package.
package mock_netceptor

import (
	context "context"
	net "net"
	reflect "reflect"
	time "time"

	quic "github.com/quic-go/quic-go"
	gomock "go.uber.org/mock/gomock"
)

// MockQuicStreamForConn is a mock of QuicStreamForConn interface.
type MockQuicStreamForConn struct {
	ctrl     *gomock.Controller
	recorder *MockQuicStreamForConnMockRecorder
	isgomock struct{}
}

// MockQuicStreamForConnMockRecorder is the mock recorder for MockQuicStreamForConn.
type MockQuicStreamForConnMockRecorder struct {
	mock *MockQuicStreamForConn
}

// NewMockQuicStreamForConn creates a new mock instance.
func NewMockQuicStreamForConn(ctrl *gomock.Controller) *MockQuicStreamForConn {
	mock := &MockQuicStreamForConn{ctrl: ctrl}
	mock.recorder = &MockQuicStreamForConnMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuicStreamForConn) EXPECT() *MockQuicStreamForConnMockRecorder {
	return m.recorder
}

// CancelRead mocks base method.
func (m *MockQuicStreamForConn) CancelRead(arg0 quic.StreamErrorCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CancelRead", arg0)
}

// CancelRead indicates an expected call of CancelRead.
func (mr *MockQuicStreamForConnMockRecorder) CancelRead(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelRead", reflect.TypeOf((*MockQuicStreamForConn)(nil).CancelRead), arg0)
}

// CancelWrite mocks base method.
func (m *MockQuicStreamForConn) CancelWrite(arg0 quic.StreamErrorCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CancelWrite", arg0)
}

// CancelWrite indicates an expected call of CancelWrite.
func (mr *MockQuicStreamForConnMockRecorder) CancelWrite(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelWrite", reflect.TypeOf((*MockQuicStreamForConn)(nil).CancelWrite), arg0)
}

// Close mocks base method.
func (m *MockQuicStreamForConn) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockQuicStreamForConnMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockQuicStreamForConn)(nil).Close))
}

// Context mocks base method.
func (m *MockQuicStreamForConn) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockQuicStreamForConnMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockQuicStreamForConn)(nil).Context))
}

// Read mocks base method.
func (m *MockQuicStreamForConn) Read(p []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Read", p)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Read indicates an expected call of Read.
func (mr *MockQuicStreamForConnMockRecorder) Read(p any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Read", reflect.TypeOf((*MockQuicStreamForConn)(nil).Read), p)
}

// SetDeadline mocks base method.
func (m *MockQuicStreamForConn) SetDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeadline indicates an expected call of SetDeadline.
func (mr *MockQuicStreamForConnMockRecorder) SetDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeadline", reflect.TypeOf((*MockQuicStreamForConn)(nil).SetDeadline), t)
}

// SetReadDeadline mocks base method.
func (m *MockQuicStreamForConn) SetReadDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReadDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReadDeadline indicates an expected call of SetReadDeadline.
func (mr *MockQuicStreamForConnMockRecorder) SetReadDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReadDeadline", reflect.TypeOf((*MockQuicStreamForConn)(nil).SetReadDeadline), t)
}

// SetWriteDeadline mocks base method.
func (m *MockQuicStreamForConn) SetWriteDeadline(t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWriteDeadline", t)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWriteDeadline indicates an expected call of SetWriteDeadline.
func (mr *MockQuicStreamForConnMockRecorder) SetWriteDeadline(t any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWriteDeadline", reflect.TypeOf((*MockQuicStreamForConn)(nil).SetWriteDeadline), t)
}

// StreamID mocks base method.
func (m *MockQuicStreamForConn) StreamID() quic.StreamID {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StreamID")
	ret0, _ := ret[0].(quic.StreamID)
	return ret0
}

// StreamID indicates an expected call of StreamID.
func (mr *MockQuicStreamForConnMockRecorder) StreamID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamID", reflect.TypeOf((*MockQuicStreamForConn)(nil).StreamID))
}

// Write mocks base method.
func (m *MockQuicStreamForConn) Write(p []byte) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Write", p)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Write indicates an expected call of Write.
func (mr *MockQuicStreamForConnMockRecorder) Write(p any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Write", reflect.TypeOf((*MockQuicStreamForConn)(nil).Write), p)
}

// MockQuicConnectionForConn is a mock of QuicConnectionForConn interface.
type MockQuicConnectionForConn struct {
	ctrl     *gomock.Controller
	recorder *MockQuicConnectionForConnMockRecorder
	isgomock struct{}
}

// MockQuicConnectionForConnMockRecorder is the mock recorder for MockQuicConnectionForConn.
type MockQuicConnectionForConnMockRecorder struct {
	mock *MockQuicConnectionForConn
}

// NewMockQuicConnectionForConn creates a new mock instance.
func NewMockQuicConnectionForConn(ctrl *gomock.Controller) *MockQuicConnectionForConn {
	mock := &MockQuicConnectionForConn{ctrl: ctrl}
	mock.recorder = &MockQuicConnectionForConnMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuicConnectionForConn) EXPECT() *MockQuicConnectionForConnMockRecorder {
	return m.recorder
}

// AcceptStream mocks base method.
func (m *MockQuicConnectionForConn) AcceptStream(arg0 context.Context) (quic.Stream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptStream", arg0)
	ret0, _ := ret[0].(quic.Stream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptStream indicates an expected call of AcceptStream.
func (mr *MockQuicConnectionForConnMockRecorder) AcceptStream(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptStream", reflect.TypeOf((*MockQuicConnectionForConn)(nil).AcceptStream), arg0)
}

// AcceptUniStream mocks base method.
func (m *MockQuicConnectionForConn) AcceptUniStream(arg0 context.Context) (quic.ReceiveStream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptUniStream", arg0)
	ret0, _ := ret[0].(quic.ReceiveStream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptUniStream indicates an expected call of AcceptUniStream.
func (mr *MockQuicConnectionForConnMockRecorder) AcceptUniStream(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptUniStream", reflect.TypeOf((*MockQuicConnectionForConn)(nil).AcceptUniStream), arg0)
}

// CloseWithError mocks base method.
func (m *MockQuicConnectionForConn) CloseWithError(arg0 quic.ApplicationErrorCode, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseWithError", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseWithError indicates an expected call of CloseWithError.
func (mr *MockQuicConnectionForConnMockRecorder) CloseWithError(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseWithError", reflect.TypeOf((*MockQuicConnectionForConn)(nil).CloseWithError), arg0, arg1)
}

// ConnectionState mocks base method.
func (m *MockQuicConnectionForConn) ConnectionState() quic.ConnectionState {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConnectionState")
	ret0, _ := ret[0].(quic.ConnectionState)
	return ret0
}

// ConnectionState indicates an expected call of ConnectionState.
func (mr *MockQuicConnectionForConnMockRecorder) ConnectionState() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConnectionState", reflect.TypeOf((*MockQuicConnectionForConn)(nil).ConnectionState))
}

// Context mocks base method.
func (m *MockQuicConnectionForConn) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockQuicConnectionForConnMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockQuicConnectionForConn)(nil).Context))
}

// LocalAddr mocks base method.
func (m *MockQuicConnectionForConn) LocalAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LocalAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// LocalAddr indicates an expected call of LocalAddr.
func (mr *MockQuicConnectionForConnMockRecorder) LocalAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LocalAddr", reflect.TypeOf((*MockQuicConnectionForConn)(nil).LocalAddr))
}

// OpenStream mocks base method.
func (m *MockQuicConnectionForConn) OpenStream() (quic.Stream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenStream")
	ret0, _ := ret[0].(quic.Stream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenStream indicates an expected call of OpenStream.
func (mr *MockQuicConnectionForConnMockRecorder) OpenStream() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenStream", reflect.TypeOf((*MockQuicConnectionForConn)(nil).OpenStream))
}

// OpenStreamSync mocks base method.
func (m *MockQuicConnectionForConn) OpenStreamSync(arg0 context.Context) (quic.Stream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenStreamSync", arg0)
	ret0, _ := ret[0].(quic.Stream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenStreamSync indicates an expected call of OpenStreamSync.
func (mr *MockQuicConnectionForConnMockRecorder) OpenStreamSync(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenStreamSync", reflect.TypeOf((*MockQuicConnectionForConn)(nil).OpenStreamSync), arg0)
}

// OpenUniStream mocks base method.
func (m *MockQuicConnectionForConn) OpenUniStream() (quic.SendStream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenUniStream")
	ret0, _ := ret[0].(quic.SendStream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenUniStream indicates an expected call of OpenUniStream.
func (mr *MockQuicConnectionForConnMockRecorder) OpenUniStream() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenUniStream", reflect.TypeOf((*MockQuicConnectionForConn)(nil).OpenUniStream))
}

// OpenUniStreamSync mocks base method.
func (m *MockQuicConnectionForConn) OpenUniStreamSync(arg0 context.Context) (quic.SendStream, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenUniStreamSync", arg0)
	ret0, _ := ret[0].(quic.SendStream)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenUniStreamSync indicates an expected call of OpenUniStreamSync.
func (mr *MockQuicConnectionForConnMockRecorder) OpenUniStreamSync(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenUniStreamSync", reflect.TypeOf((*MockQuicConnectionForConn)(nil).OpenUniStreamSync), arg0)
}

// ReceiveDatagram mocks base method.
func (m *MockQuicConnectionForConn) ReceiveDatagram(arg0 context.Context) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReceiveDatagram", arg0)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReceiveDatagram indicates an expected call of ReceiveDatagram.
func (mr *MockQuicConnectionForConnMockRecorder) ReceiveDatagram(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReceiveDatagram", reflect.TypeOf((*MockQuicConnectionForConn)(nil).ReceiveDatagram), arg0)
}

// RemoteAddr mocks base method.
func (m *MockQuicConnectionForConn) RemoteAddr() net.Addr {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoteAddr")
	ret0, _ := ret[0].(net.Addr)
	return ret0
}

// RemoteAddr indicates an expected call of RemoteAddr.
func (mr *MockQuicConnectionForConnMockRecorder) RemoteAddr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoteAddr", reflect.TypeOf((*MockQuicConnectionForConn)(nil).RemoteAddr))
}

// SendDatagram mocks base method.
func (m *MockQuicConnectionForConn) SendDatagram(payload []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendDatagram", payload)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendDatagram indicates an expected call of SendDatagram.
func (mr *MockQuicConnectionForConnMockRecorder) SendDatagram(payload any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendDatagram", reflect.TypeOf((*MockQuicConnectionForConn)(nil).SendDatagram), payload)
}

// MockQuicListenerForConn is a mock of QuicListenerForConn interface.
type MockQuicListenerForConn struct {
	ctrl     *gomock.Controller
	recorder *MockQuicListenerForConnMockRecorder
	isgomock struct{}
}

// MockQuicListenerForConnMockRecorder is the mock recorder for MockQuicListenerForConn.
type MockQuicListenerForConnMockRecorder struct {
	mock *MockQuicListenerForConn
}

// NewMockQuicListenerForConn creates a new mock instance.
func NewMockQuicListenerForConn(ctrl *gomock.Controller) *MockQuicListenerForConn {
	mock := &MockQuicListenerForConn{ctrl: ctrl}
	mock.recorder = &MockQuicListenerForConnMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuicListenerForConn) EXPECT() *MockQuicListenerForConnMockRecorder {
	return m.recorder
}

// Accept mocks base method.
func (m *MockQuicListenerForConn) Accept(arg0 context.Context) (quic.Connection, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Accept", arg0)
	ret0, _ := ret[0].(quic.Connection)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Accept indicates an expected call of Accept.
func (mr *MockQuicListenerForConnMockRecorder) Accept(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Accept", reflect.TypeOf((*MockQuicListenerForConn)(nil).Accept), arg0)
}

// Close mocks base method.
func (m *MockQuicListenerForConn) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockQuicListenerForConnMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockQuicListenerForConn)(nil).Close))
}
